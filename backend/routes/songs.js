const express = require("express");
const { Song, songs } = require("../data/songs");
const router = express.Router();

// Validation middleware
const validateSong = (req, res, next) => {
    const { title, artist, album, year, genre, duration } = req.body;

    if (!title || !artist || !album || !year || !genre || !duration) {
        return res.status(400).json({
            error: "Validation Error",
            message:
                "All fields are required: title, artist, album, year, genre, duration",
        });
    }

    if (
        typeof year !== "number" ||
        year < 1900 ||
        year > new Date().getFullYear()
    ) {
        return res.status(400).json({
            error: "Validation Error",
            message:
                "Year must be a valid number between 1900 and current year",
        });
    }

    next();
};

// GET /api/songs - Get all songs (frontend handles pagination and search)
router.get("/", (req, res) => {
    try {
        // Return all songs - let frontend handle pagination and search
        res.json({
            songs: songs,
            totalSongs: songs.length,
            message: "All songs retrieved successfully",
        });
    } catch (error) {
        res.status(500).json({
            error: "Server Error",
            message: "Failed to retrieve songs",
        });
    }
});

// GET /api/songs/:id - Get a specific song
router.get("/:id", (req, res) => {
    try {
        const song = songs.find((s) => s.id === req.params.id);

        if (!song) {
            return res.status(404).json({
                error: "Not Found",
                message: "Song not found",
            });
        }

        res.json(song);
    } catch (error) {
        res.status(500).json({
            error: "Server Error",
            message: "Failed to retrieve song",
        });
    }
});

// POST /api/songs - Create a new song
router.post("/", validateSong, (req, res) => {
    try {
        const newSong = new Song(req.body);
        songs.push(newSong);

        res.status(201).json(newSong);
    } catch (error) {
        res.status(500).json({
            error: "Server Error",
            message: "Failed to create song",
        });
    }
});

// PUT /api/songs/:id - Update an existing song
router.put("/:id", validateSong, (req, res) => {
    try {
        const songIndex = songs.findIndex((s) => s.id === req.params.id);

        if (songIndex === -1) {
            return res.status(404).json({
                error: "Not Found",
                message: "Song not found",
            });
        }

        const updatedSong = {
            ...songs[songIndex],
            ...req.body,
            updatedAt: new Date().toISOString(),
        };

        songs[songIndex] = updatedSong;

        res.json(updatedSong);
    } catch (error) {
        res.status(500).json({
            error: "Server Error",
            message: "Failed to update song",
        });
    }
});

// DELETE /api/songs/:id - Delete a song
router.delete("/:id", (req, res) => {
    try {
        const songIndex = songs.findIndex((s) => s.id === req.params.id);

        if (songIndex === -1) {
            return res.status(404).json({
                error: "Not Found",
                message: "Song not found",
            });
        }

        songs.splice(songIndex, 1);

        res.json({
            message: "Song deleted successfully",
        });
    } catch (error) {
        res.status(500).json({
            error: "Server Error",
            message: "Failed to delete song",
        });
    }
});

module.exports = router;
