const express = require("express");
const { Song, songs } = require("../data/songs");
const router = express.Router();

// Helper function for pagination
const paginate = (array, page, limit) => {
    const startIndex = (page - 1) * limit;
    const endIndex = page * limit;

    const result = {};

    if (endIndex < array.length) {
        result.next = {
            page: page + 1,
            limit: limit,
        };
    }

    if (startIndex > 0) {
        result.previous = {
            page: page - 1,
            limit: limit,
        };
    }

    result.results = array.slice(startIndex, endIndex);
    result.pagination = {
        currentPage: page,
        totalPages: Math.ceil(array.length / limit),
        totalItems: array.length,
        itemsPerPage: limit,
    };

    return result;
};

// Validation middleware
const validateSong = (req, res, next) => {
    const { title, artist, album, year, genre, duration } = req.body;

    if (!title || !artist || !album || !year || !genre || !duration) {
        return res.status(400).json({
            error: "Validation Error",
            message:
                "All fields are required: title, artist, album, year, genre, duration",
        });
    }

    if (
        typeof year !== "number" ||
        year < 1900 ||
        year > new Date().getFullYear()
    ) {
        return res.status(400).json({
            error: "Validation Error",
            message:
                "Year must be a valid number between 1900 and current year",
        });
    }

    next();
};

// GET /api/songs - Get all songs with pagination and search
router.get("/", (req, res) => {
    try {
        const page = parseInt(req.query.page) || 1;
        const limit = parseInt(req.query.limit) || 10;
        const search = req.query.search || "";

        if (page < 1 || limit < 1) {
            return res.status(400).json({
                error: "Invalid Parameters",
                message: "Page and limit must be positive numbers",
            });
        }

        // Filter songs based on search query
        let filteredSongs = songs;
        if (search.trim()) {
            const searchLower = search.toLowerCase().trim();
            filteredSongs = songs.filter(
                (song) =>
                    song.title.toLowerCase().includes(searchLower) ||
                    song.artist.toLowerCase().includes(searchLower) ||
                    song.album.toLowerCase().includes(searchLower) ||
                    song.genre.toLowerCase().includes(searchLower) ||
                    song.year.toString().includes(searchLower)
            );
        }

        const paginatedResult = paginate(filteredSongs, page, limit);

        res.json({
            songs: paginatedResult.results,
            pagination: paginatedResult.pagination,
            searchQuery: search,
            totalResults: filteredSongs.length,
            ...(paginatedResult.next && { next: paginatedResult.next }),
            ...(paginatedResult.previous && {
                previous: paginatedResult.previous,
            }),
        });
    } catch (error) {
        res.status(500).json({
            error: "Server Error",
            message: "Failed to retrieve songs",
        });
    }
});

// GET /api/songs/:id - Get a specific song
router.get("/:id", (req, res) => {
    try {
        const song = songs.find((s) => s.id === req.params.id);

        if (!song) {
            return res.status(404).json({
                error: "Not Found",
                message: "Song not found",
            });
        }

        res.json(song);
    } catch (error) {
        res.status(500).json({
            error: "Server Error",
            message: "Failed to retrieve song",
        });
    }
});

// POST /api/songs - Create a new song
router.post("/", validateSong, (req, res) => {
    try {
        const newSong = new Song(req.body);
        songs.push(newSong);

        res.status(201).json(newSong);
    } catch (error) {
        res.status(500).json({
            error: "Server Error",
            message: "Failed to create song",
        });
    }
});

// PUT /api/songs/:id - Update an existing song
router.put("/:id", validateSong, (req, res) => {
    try {
        const songIndex = songs.findIndex((s) => s.id === req.params.id);

        if (songIndex === -1) {
            return res.status(404).json({
                error: "Not Found",
                message: "Song not found",
            });
        }

        const updatedSong = {
            ...songs[songIndex],
            ...req.body,
            updatedAt: new Date().toISOString(),
        };

        songs[songIndex] = updatedSong;

        res.json(updatedSong);
    } catch (error) {
        res.status(500).json({
            error: "Server Error",
            message: "Failed to update song",
        });
    }
});

// DELETE /api/songs/:id - Delete a song
router.delete("/:id", (req, res) => {
    try {
        const songIndex = songs.findIndex((s) => s.id === req.params.id);

        if (songIndex === -1) {
            return res.status(404).json({
                error: "Not Found",
                message: "Song not found",
            });
        }

        songs.splice(songIndex, 1);

        res.json({
            message: "Song deleted successfully",
        });
    } catch (error) {
        res.status(500).json({
            error: "Server Error",
            message: "Failed to delete song",
        });
    }
});

module.exports = router;
