.rate-limit-notice {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 1000;
  max-width: 400px;
  background: linear-gradient(135deg, #ff6b6b, #ffa500);
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
  animation: slideIn 0.3s ease-out;
}

.rate-limit-content {
  padding: 20px;
  color: white;
}

.rate-limit-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.rate-limit-header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
}

.close-button {
  background: rgba(255, 255, 255, 0.2);
  border: none;
  color: white;
  font-size: 24px;
  width: 30px;
  height: 30px;
  border-radius: 50%;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background-color 0.2s;
}

.close-button:hover {
  background: rgba(255, 255, 255, 0.3);
}

.rate-limit-message {
  font-size: 14px;
  margin-bottom: 12px;
  line-height: 1.4;
}

.retry-info {
  font-size: 13px;
  margin-bottom: 15px;
  padding: 8px 12px;
  background: rgba(255, 255, 255, 0.15);
  border-radius: 6px;
}

.demo-info {
  margin-bottom: 15px;
}

.demo-info h4 {
  margin: 0 0 8px 0;
  font-size: 14px;
  font-weight: 600;
}

.demo-info ul {
  margin: 0;
  padding-left: 20px;
  font-size: 13px;
}

.demo-info li {
  margin-bottom: 4px;
}

.upgrade-info {
  padding: 12px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  text-align: center;
}

.upgrade-info p {
  margin: 0 0 10px 0;
  font-size: 13px;
}

.upgrade-button {
  background: white;
  color: #ff6b6b;
  border: none;
  padding: 8px 16px;
  border-radius: 6px;
  font-size: 12px;
  font-weight: 600;
  cursor: pointer;
  transition: transform 0.2s, box-shadow 0.2s;
}

.upgrade-button:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

@keyframes slideIn {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

/* Mobile responsiveness */
@media (max-width: 480px) {
  .rate-limit-notice {
    top: 10px;
    right: 10px;
    left: 10px;
    max-width: none;
  }
  
  .rate-limit-content {
    padding: 15px;
  }
  
  .rate-limit-header h3 {
    font-size: 16px;
  }
}
