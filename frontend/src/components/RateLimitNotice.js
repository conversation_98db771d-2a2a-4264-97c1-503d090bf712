import React, { useState, useEffect } from 'react';
import './RateLimitNotice.css';

const RateLimitNotice = ({ rateLimitInfo, onClose }) => {
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    if (rateLimitInfo) {
      setIsVisible(true);
      // Auto-hide after 10 seconds
      const timer = setTimeout(() => {
        setIsVisible(false);
        if (onClose) onClose();
      }, 10000);

      return () => clearTimeout(timer);
    }
  }, [rateLimitInfo, onClose]);

  if (!isVisible || !rateLimitInfo) {
    return null;
  }

  const handleClose = () => {
    setIsVisible(false);
    if (onClose) onClose();
  };

  return (
    <div className="rate-limit-notice">
      <div className="rate-limit-content">
        <div className="rate-limit-header">
          <h3>🚦 Demo Tier Rate Limit</h3>
          <button className="close-button" onClick={handleClose}>
            ×
          </button>
        </div>
        
        <div className="rate-limit-body">
          <p className="rate-limit-message">{rateLimitInfo.message}</p>
          
          {rateLimitInfo.retryAfter && (
            <p className="retry-info">
              <strong>Retry after:</strong> {rateLimitInfo.retryAfter}
            </p>
          )}
          
          <div className="demo-info">
            <h4>Demo Tier Limits:</h4>
            <ul>
              <li>100 requests per hour</li>
              <li>10 requests per minute</li>
              <li>Slower response times after burst limits</li>
            </ul>
          </div>
          
          {rateLimitInfo.upgradeInfo && (
            <div className="upgrade-info">
              <p><strong>💡 {rateLimitInfo.upgradeInfo}</strong></p>
              <button className="upgrade-button">
                Learn About Upgrading
              </button>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default RateLimitNotice;
