import React, { useState, useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import styled from "@emotion/styled";
import Modal from "./Modal";
import {
    createSongRequest,
    updateSongRequest,
    clearCurrentSong,
} from "../store/slices/songsSlice";
import { closeCreateModal, closeEditModal } from "../store/slices/uiSlice";

const Form = styled.form`
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
`;

const FormGroup = styled.div`
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
`;

const Label = styled.label`
    font-weight: 600;
    color: #495057;
    font-size: 0.9rem;
`;

const Input = styled.input`
    padding: 0.75rem;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    font-size: 1rem;
    transition: border-color 0.2s ease;

    &:focus {
        outline: none;
        border-color: #667eea;
    }

    &:invalid {
        border-color: #dc3545;
    }
`;

const Select = styled.select`
    padding: 0.75rem;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    font-size: 1rem;
    background: white;
    transition: border-color 0.2s ease;

    &:focus {
        outline: none;
        border-color: #667eea;
    }
`;

const ButtonGroup = styled.div`
    display: flex;
    gap: 1rem;
    justify-content: flex-end;
    margin-top: 1rem;
`;

const Button = styled.button`
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 8px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
    border: 2px solid #131314;
    border-bottom: 5px solid #131314;
    &.primary {
        background: #667eea;
        color: white;

        &:hover {
            background: #5a6fd8;
        }

        &:disabled {
            background: #adb5bd;
            cursor: not-allowed;
        }
    }

    &.secondary {
        background: #6c757d;
        color: white;

        &:hover {
            background: #5a6268;
        }
    }
`;

const ErrorText = styled.span`
    color: #dc3545;
    font-size: 0.875rem;
`;

const genres = [
    "Rock",
    "Pop",
    "Hip Hop",
    "Jazz",
    "Classical",
    "Electronic",
    "Country",
    "R&B",
    "Folk",
    "Blues",
    "Reggae",
    "Punk",
    "Metal",
    "Alternative",
    "Indie",
    "Soul",
    "Funk",
    "Disco",
    "House",
    "Techno",
    "Other",
];

function SongForm() {
    const dispatch = useDispatch();
    const { isCreateModalOpen, isEditModalOpen } = useSelector(
        (state) => state.ui
    );
    const { currentSong, loading } = useSelector((state) => state.songs);

    const isOpen = isCreateModalOpen || isEditModalOpen;
    const isEditing = isEditModalOpen && currentSong;

    const [formData, setFormData] = useState({
        title: "",
        artist: "",
        album: "",
        year: new Date().getFullYear(),
        genre: "",
        duration: "",
    });

    const [errors, setErrors] = useState({});

    useEffect(() => {
        if (isEditing) {
            setFormData({
                title: currentSong.title || "",
                artist: currentSong.artist || "",
                album: currentSong.album || "",
                year: currentSong.year || new Date().getFullYear(),
                genre: currentSong.genre || "",
                duration: currentSong.duration || "",
            });
        } else {
            setFormData({
                title: "",
                artist: "",
                album: "",
                year: new Date().getFullYear(),
                genre: "",
                duration: "",
            });
        }
        setErrors({});
    }, [isEditing, currentSong]);

    const validateForm = () => {
        const newErrors = {};

        if (!formData.title.trim()) {
            newErrors.title = "Title is required";
        }

        if (!formData.artist.trim()) {
            newErrors.artist = "Artist is required";
        }

        if (!formData.album.trim()) {
            newErrors.album = "Album is required";
        }

        if (
            !formData.year ||
            formData.year < 1900 ||
            formData.year > new Date().getFullYear()
        ) {
            newErrors.year = "Please enter a valid year";
        }

        if (!formData.genre) {
            newErrors.genre = "Genre is required";
        }

        if (!formData.duration.trim()) {
            newErrors.duration = "Duration is required";
        } else if (!/^\d{1,2}:\d{2}$/.test(formData.duration)) {
            newErrors.duration =
                "Duration must be in MM:SS format (e.g., 3:45)";
        }

        setErrors(newErrors);
        return Object.keys(newErrors).length === 0;
    };

    const handleSubmit = (e) => {
        e.preventDefault();

        if (!validateForm()) {
            return;
        }

        const songData = {
            ...formData,
            year: parseInt(formData.year),
        };

        if (isEditing) {
            dispatch(updateSongRequest({ id: currentSong.id, ...songData }));
        } else {
            dispatch(createSongRequest(songData));
        }
    };

    const handleClose = () => {
        if (isCreateModalOpen) {
            dispatch(closeCreateModal());
        } else if (isEditModalOpen) {
            dispatch(closeEditModal());
            dispatch(clearCurrentSong());
        }
        setFormData({
            title: "",
            artist: "",
            album: "",
            year: new Date().getFullYear(),
            genre: "",
            duration: "",
        });
        setErrors({});
    };

    const handleChange = (e) => {
        const { name, value } = e.target;
        setFormData((prev) => ({
            ...prev,
            [name]: value,
        }));

        // Clear error when user starts typing
        if (errors[name]) {
            setErrors((prev) => ({
                ...prev,
                [name]: "",
            }));
        }
    };

    return (
        <Modal
            isOpen={isOpen}
            onClose={handleClose}
            title={isEditing ? "Edit Song" : "Add New Song"}
        >
            <Form onSubmit={handleSubmit}>
                <FormGroup>
                    <Label htmlFor="title">Title *</Label>
                    <Input
                        id="title"
                        name="title"
                        type="text"
                        value={formData.title}
                        onChange={handleChange}
                        placeholder="Enter song title"
                        required
                    />
                    {errors.title && <ErrorText>{errors.title}</ErrorText>}
                </FormGroup>

                <FormGroup>
                    <Label htmlFor="artist">Artist *</Label>
                    <Input
                        id="artist"
                        name="artist"
                        type="text"
                        value={formData.artist}
                        onChange={handleChange}
                        placeholder="Enter artist name"
                        required
                    />
                    {errors.artist && <ErrorText>{errors.artist}</ErrorText>}
                </FormGroup>

                <FormGroup>
                    <Label htmlFor="album">Album *</Label>
                    <Input
                        id="album"
                        name="album"
                        type="text"
                        value={formData.album}
                        onChange={handleChange}
                        placeholder="Enter album name"
                        required
                    />
                    {errors.album && <ErrorText>{errors.album}</ErrorText>}
                </FormGroup>

                <FormGroup>
                    <Label htmlFor="year">Year *</Label>
                    <Input
                        id="year"
                        name="year"
                        type="number"
                        min="1900"
                        max={new Date().getFullYear()}
                        value={formData.year}
                        onChange={handleChange}
                        required
                    />
                    {errors.year && <ErrorText>{errors.year}</ErrorText>}
                </FormGroup>

                <FormGroup>
                    <Label htmlFor="genre">Genre *</Label>
                    <Select
                        id="genre"
                        name="genre"
                        value={formData.genre}
                        onChange={handleChange}
                        required
                    >
                        <option value="">Select a genre</option>
                        {genres.map((genre) => (
                            <option key={genre} value={genre}>
                                {genre}
                            </option>
                        ))}
                    </Select>
                    {errors.genre && <ErrorText>{errors.genre}</ErrorText>}
                </FormGroup>

                <FormGroup>
                    <Label htmlFor="duration">Duration *</Label>
                    <Input
                        id="duration"
                        name="duration"
                        type="text"
                        value={formData.duration}
                        onChange={handleChange}
                        placeholder="e.g., 3:45"
                        pattern="\d{1,2}:\d{2}"
                        required
                    />
                    {errors.duration && (
                        <ErrorText>{errors.duration}</ErrorText>
                    )}
                </FormGroup>

                <ButtonGroup>
                    <Button
                        type="button"
                        className="secondary"
                        onClick={handleClose}
                    >
                        Cancel
                    </Button>
                    <Button
                        type="submit"
                        className="primary"
                        disabled={loading}
                    >
                        {loading
                            ? "Saving..."
                            : isEditing
                            ? "Update Song"
                            : "Add Song"}
                    </Button>
                </ButtonGroup>
            </Form>
        </Modal>
    );
}

export default SongForm;
