import React from "react";
import { createRoot } from "react-dom/client";
import { Provider } from "react-redux";
import { ThemeProvider } from "@emotion/react";
import AOS from "aos";
import "aos/dist/aos.css";
import App from "./App";
import { store } from "./store/store";
import { theme } from "./styles/theme";
import "./styles/global.css";

// Initialize AOS
AOS.init({
    duration: 800, // Animation duration in milliseconds
    easing: "ease-in-out", // Easing function
    once: true, // Whether animation should happen only once
    mirror: false, // Whether elements should animate out while scrolling past them
    offset: 100, // Offset (in px) from the original trigger point
});

const container = document.getElementById("root");
const root = createRoot(container);

root.render(
    <React.StrictMode>
        <Provider store={store}>
            <ThemeProvider theme={theme}>
                <App />
            </ThemeProvider>
        </Provider>
    </React.StrictMode>
);
