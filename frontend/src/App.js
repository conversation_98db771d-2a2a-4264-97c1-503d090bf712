import React from "react";
import styled from "@emotion/styled";
import SongManagement from "./pages/SongManagement";

const AppContainer = styled.div`
    min-height: 100vh;
    max-width: 1200px;
    margin-inline: auto;
    padding: 20px;
`;

const Header = styled.header`
    text-align: left;
    margin-bottom: 2rem;
    color: white;
`;

const Title = styled.h1`
    font-size: 2.5rem;
    font-weight: 900;
    color: #131314;
`;

const SubContainer = styled.div`
    display: flex;
    flex-direction: row;
    align-items: center;
    gap: 0.5rem;
`;
const Subtitle = styled.p`
    font-size: 1.1rem;
    font-weight: 500;
    color: #131314;
`;

const Fun = styled.p`
    font-weight: 800;
`;

function App() {
    return (
        <AppContainer>
            <Header>
                <Title>Addis Software Internship Test</Title>
                <SubContainer>
                    <Subtitle>by</Subtitle>
                    <Subtitle>
                        <Fun>Yonatan</Fun>
                    </Subtitle>
                    <Subtitle>Afewerk Teshome</Subtitle>
                </SubContainer>
            </Header>
            <SongManagement />
        </AppContainer>
    );
}

export default App;
