import { call, put, takeEvery, takeLatest, select } from "redux-saga/effects";
import {
    fetchSongsRequest,
    fetchSongsSuccess,
    fetchSongsFailure,
    createSongRequest,
    createSongSuccess,
    createSongFailure,
    updateSongRequest,
    updateSongSuccess,
    updateSongFailure,
    deleteSongRequest,
    deleteSongSuccess,
    deleteSongFailure,
    setSearchQuery,
} from "../slices/songsSlice";
import { addNotification } from "../slices/uiSlice";
import * as songsAPI from "../../services/songsAPI";

// Fetch songs saga
function* fetchSongsSaga(action) {
    try {
        const { page = 1, limit = 10, search = "" } = action.payload || {};
        const response = yield call(songsAPI.fetchSongs, {
            page,
            limit,
            search,
        });
        yield put(fetchSongsSuccess(response.data));
    } catch (error) {
        const errorMessage =
            error.response?.data?.message || "Failed to fetch songs";
        yield put(fetchSongsFailure(errorMessage));
        yield put(
            addNotification({
                type: "error",
                message: errorMessage,
            })
        );
    }
}

// Create song saga
function* createSongSaga(action) {
    try {
        const response = yield call(songsAPI.createSong, action.payload);
        yield put(createSongSuccess(response.data));
        yield put(
            addNotification({
                type: "success",
                message: "Song created successfully!",
            })
        );
    } catch (error) {
        const errorMessage =
            error.response?.data?.message || "Failed to create song";
        yield put(createSongFailure(errorMessage));
        yield put(
            addNotification({
                type: "error",
                message: errorMessage,
            })
        );
    }
}

// Update song saga
function* updateSongSaga(action) {
    try {
        const { id, ...songData } = action.payload;
        const response = yield call(songsAPI.updateSong, id, songData);
        yield put(updateSongSuccess(response.data));
        yield put(
            addNotification({
                type: "success",
                message: "Song updated successfully!",
            })
        );
    } catch (error) {
        const errorMessage =
            error.response?.data?.message || "Failed to update song";
        yield put(updateSongFailure(errorMessage));
        yield put(
            addNotification({
                type: "error",
                message: errorMessage,
            })
        );
    }
}

// Delete song saga
function* deleteSongSaga(action) {
    try {
        yield call(songsAPI.deleteSong, action.payload);
        yield put(deleteSongSuccess(action.payload));
        yield put(
            addNotification({
                type: "success",
                message: "Song deleted successfully!",
            })
        );
    } catch (error) {
        const errorMessage =
            error.response?.data?.message || "Failed to delete song";
        yield put(deleteSongFailure(errorMessage));
        yield put(
            addNotification({
                type: "error",
                message: errorMessage,
            })
        );
    }
}

// Search saga - triggers when search query changes
function* searchSongsSaga(action) {
    try {
        // Get current pagination state
        const state = yield select();
        const { pagination } = state.songs;

        // Fetch songs with search query, reset to page 1
        yield put(
            fetchSongsRequest({
                page: 1,
                limit: pagination.itemsPerPage,
                search: action.payload,
            })
        );
    } catch (error) {
        console.error("Search saga error:", error);
    }
}

// Watcher sagas
export default function* songsSaga() {
    yield takeLatest(fetchSongsRequest.type, fetchSongsSaga);
    yield takeEvery(createSongRequest.type, createSongSaga);
    yield takeEvery(updateSongRequest.type, updateSongSaga);
    yield takeEvery(deleteSongRequest.type, deleteSongSaga);
    yield takeLatest(setSearchQuery.type, searchSongsSaga);
}
