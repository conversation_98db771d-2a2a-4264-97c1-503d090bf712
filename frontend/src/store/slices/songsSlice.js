import { createSlice } from "@reduxjs/toolkit";

// Helper function to filter and paginate songs on the frontend
const filterAndPaginateSongs = (
    allSongs,
    searchQuery,
    currentPage,
    itemsPerPage,
    sortBy,
    sortOrder
) => {
    // Filter songs based on search query
    let filteredSongs = allSongs;
    if (searchQuery && searchQuery.trim()) {
        const searchLower = searchQuery.toLowerCase().trim();
        filteredSongs = allSongs.filter(
            (song) =>
                song.title.toLowerCase().includes(searchLower) ||
                song.artist.toLowerCase().includes(searchLower) ||
                song.album.toLowerCase().includes(searchLower) ||
                song.genre.toLowerCase().includes(searchLower) ||
                song.year.toString().includes(searchLower)
        );
    }

    // Sort songs
    filteredSongs.sort((a, b) => {
        let aValue = a[sortBy];
        let bValue = b[sortBy];

        // Handle different data types
        if (typeof aValue === "string") {
            aValue = aValue.toLowerCase();
            bValue = bValue.toLowerCase();
        }

        if (sortOrder === "asc") {
            return aValue < bValue ? -1 : aValue > bValue ? 1 : 0;
        } else {
            return aValue > bValue ? -1 : aValue < bValue ? 1 : 0;
        }
    });

    // Calculate pagination
    const totalItems = filteredSongs.length;
    const totalPages = Math.ceil(totalItems / itemsPerPage);
    const startIndex = (currentPage - 1) * itemsPerPage;
    const endIndex = startIndex + itemsPerPage;
    const paginatedSongs = filteredSongs.slice(startIndex, endIndex);

    return {
        songs: paginatedSongs,
        pagination: {
            currentPage,
            totalPages,
            totalItems,
            itemsPerPage,
        },
    };
};

const initialState = {
    allSongs: [], // All songs from the server
    songs: [], // Filtered and paginated songs for display
    currentSong: null,
    pagination: {
        currentPage: 1,
        totalPages: 1,
        totalItems: 0,
        itemsPerPage: 10,
    },
    loading: false,
    error: null,
    searchQuery: "",
    sortBy: "title",
    sortOrder: "asc",
};

const songsSlice = createSlice({
    name: "songs",
    initialState,
    reducers: {
        // Fetch songs actions
        fetchSongsRequest: (state, action) => {
            state.loading = true;
            state.error = null;
        },
        fetchSongsSuccess: (state, action) => {
            state.loading = false;
            state.allSongs = action.payload.songs;

            // Apply current filters and pagination
            const result = filterAndPaginateSongs(
                action.payload.songs,
                state.searchQuery,
                state.pagination.currentPage,
                state.pagination.itemsPerPage,
                state.sortBy,
                state.sortOrder
            );

            state.songs = result.songs;
            state.pagination = result.pagination;
            state.error = null;
        },
        fetchSongsFailure: (state, action) => {
            state.loading = false;
            state.error = action.payload;
        },

        // Create song actions
        createSongRequest: (state, action) => {
            state.loading = true;
            state.error = null;
        },
        createSongSuccess: (state, action) => {
            state.loading = false;
            state.allSongs.unshift(action.payload);

            // Reapply filters and pagination
            const result = filterAndPaginateSongs(
                state.allSongs,
                state.searchQuery,
                state.pagination.currentPage,
                state.pagination.itemsPerPage,
                state.sortBy,
                state.sortOrder
            );

            state.songs = result.songs;
            state.pagination = result.pagination;
            state.error = null;
        },
        createSongFailure: (state, action) => {
            state.loading = false;
            state.error = action.payload;
        },

        // Update song actions
        updateSongRequest: (state, action) => {
            state.loading = true;
            state.error = null;
        },
        updateSongSuccess: (state, action) => {
            state.loading = false;
            const index = state.allSongs.findIndex(
                (song) => song.id === action.payload.id
            );
            if (index !== -1) {
                state.allSongs[index] = action.payload;
            }

            // Reapply filters and pagination
            const result = filterAndPaginateSongs(
                state.allSongs,
                state.searchQuery,
                state.pagination.currentPage,
                state.pagination.itemsPerPage,
                state.sortBy,
                state.sortOrder
            );

            state.songs = result.songs;
            state.pagination = result.pagination;
            state.error = null;
        },
        updateSongFailure: (state, action) => {
            state.loading = false;
            state.error = action.payload;
        },

        // Delete song actions
        deleteSongRequest: (state, action) => {
            state.loading = true;
            state.error = null;
        },
        deleteSongSuccess: (state, action) => {
            state.loading = false;
            state.allSongs = state.allSongs.filter(
                (song) => song.id !== action.payload
            );

            // Reapply filters and pagination
            const result = filterAndPaginateSongs(
                state.allSongs,
                state.searchQuery,
                state.pagination.currentPage,
                state.pagination.itemsPerPage,
                state.sortBy,
                state.sortOrder
            );

            state.songs = result.songs;
            state.pagination = result.pagination;
            state.error = null;
        },
        deleteSongFailure: (state, action) => {
            state.loading = false;
            state.error = action.payload;
        },

        // UI state actions
        setCurrentSong: (state, action) => {
            state.currentSong = action.payload;
        },
        clearCurrentSong: (state) => {
            state.currentSong = null;
        },
        setSearchQuery: (state, action) => {
            state.searchQuery = action.payload;
            state.pagination.currentPage = 1; // Reset to first page when searching

            // Reapply filters and pagination
            const result = filterAndPaginateSongs(
                state.allSongs,
                action.payload,
                1, // Reset to page 1
                state.pagination.itemsPerPage,
                state.sortBy,
                state.sortOrder
            );

            state.songs = result.songs;
            state.pagination = result.pagination;
        },
        setSortBy: (state, action) => {
            state.sortBy = action.payload;

            // Reapply filters and pagination
            const result = filterAndPaginateSongs(
                state.allSongs,
                state.searchQuery,
                state.pagination.currentPage,
                state.pagination.itemsPerPage,
                action.payload,
                state.sortOrder
            );

            state.songs = result.songs;
            state.pagination = result.pagination;
        },
        setSortOrder: (state, action) => {
            state.sortOrder = action.payload;

            // Reapply filters and pagination
            const result = filterAndPaginateSongs(
                state.allSongs,
                state.searchQuery,
                state.pagination.currentPage,
                state.pagination.itemsPerPage,
                state.sortBy,
                action.payload
            );

            state.songs = result.songs;
            state.pagination = result.pagination;
        },
        setCurrentPage: (state, action) => {
            state.pagination.currentPage = action.payload;

            // Reapply filters and pagination
            const result = filterAndPaginateSongs(
                state.allSongs,
                state.searchQuery,
                action.payload,
                state.pagination.itemsPerPage,
                state.sortBy,
                state.sortOrder
            );

            state.songs = result.songs;
            state.pagination = result.pagination;
        },
        clearError: (state) => {
            state.error = null;
        },
    },
});

export const {
    fetchSongsRequest,
    fetchSongsSuccess,
    fetchSongsFailure,
    createSongRequest,
    createSongSuccess,
    createSongFailure,
    updateSongRequest,
    updateSongSuccess,
    updateSongFailure,
    deleteSongRequest,
    deleteSongSuccess,
    deleteSongFailure,
    setCurrentSong,
    clearCurrentSong,
    setSearchQuery,
    setSortBy,
    setSortOrder,
    setCurrentPage,
    clearError,
} = songsSlice.actions;

export default songsSlice.reducer;
