import React, { useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import styled from "@emotion/styled";
import SongList from "../components/SongList";
import SongForm from "../components/SongForm";
import DeleteConfirmModal from "../components/DeleteConfirmModal";
import Pagination from "../components/Pagination";
import SearchBar from "../components/SearchBar";
import LoadingSpinner from "../components/LoadingSpinner";
import ErrorMessage from "../components/ErrorMessage";
import Notifications from "../components/Notifications";
import { fetchSongsRequest, setCurrentPage } from "../store/slices/songsSlice";
import { openCreateModal } from "../store/slices/uiSlice";

const Container = styled.div`
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 1rem;
`;

const Header = styled.div`
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    flex-wrap: wrap;
    gap: 1rem;

    @media (max-width: 768px) {
        flex-direction: column;
        align-items: stretch;
    }
`;

const HeaderLeft = styled.div`
    display: flex;
    align-items: center;
    gap: 1rem;
    flex: 1;
`;

const HeaderRight = styled.div`
    display: flex;
    gap: 1rem;
    align-items: center;
`;

const AddButton = styled.button`
    background: #76ca76ff;
    color: white;
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: 8px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    border: 2px solid #131314;
    border-bottom: 5px solid #131314;
    &:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
    }

    &:active {
        transform: translateY(0);
        outline: none;
    }
`;

const ContentArea = styled.div`
    background: white;
    border-radius: 12px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    overflow: hidden;
`;

const StatsBar = styled.div`
    background: #f8f9fa;
    padding: 1rem 1.5rem;
    border-bottom: 1px solid #e9ecef;
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 0.9rem;
    color: #6c757d;
`;

function SongManagement() {
    const dispatch = useDispatch();
    const { songs, pagination, loading, error } = useSelector(
        (state) => state.songs
    );

    useEffect(() => {
        dispatch(fetchSongsRequest({ page: 1, limit: 10 }));
    }, [dispatch]);

    const handlePageChange = (page) => {
        dispatch(setCurrentPage(page));
        dispatch(fetchSongsRequest({ page, limit: pagination.itemsPerPage }));
    };

    const handleAddSong = () => {
        dispatch(openCreateModal());
    };

    if (loading && songs.length === 0) {
        return (
            <Container>
                <LoadingSpinner />
            </Container>
        );
    }

    return (
        <Container>
            <Notifications />

            <Header>
                <HeaderLeft>
                    <SearchBar />
                </HeaderLeft>
                <HeaderRight>
                    <AddButton onClick={handleAddSong}>
                        ➕ Add New Song
                    </AddButton>
                </HeaderRight>
            </Header>

            {error && <ErrorMessage message={error} />}

            <ContentArea>
                <StatsBar>
                    <span>
                        Showing {songs.length} of {pagination.totalItems} songs
                    </span>
                    <span>
                        Page {pagination.currentPage} of {pagination.totalPages}
                    </span>
                </StatsBar>

                <SongList songs={songs} loading={loading} />

                {pagination.totalPages > 1 && (
                    <Pagination
                        currentPage={pagination.currentPage}
                        totalPages={pagination.totalPages}
                        onPageChange={handlePageChange}
                    />
                )}
            </ContentArea>

            <SongForm />
            <DeleteConfirmModal />
        </Container>
    );
}

export default SongManagement;
