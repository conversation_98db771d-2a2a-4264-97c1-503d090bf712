import axios from "axios";

// Create axios instance with base configuration
const api = axios.create({
    baseURL:
        process.env.NODE_ENV === "production"
            ? "/api"
            : "http://localhost:5000/api",
    timeout: 10000,
    headers: {
        "Content-Type": "application/json",
    },
});

// Request interceptor
api.interceptors.request.use(
    (config) => {
        // Add any auth headers here if needed
        return config;
    },
    (error) => {
        return Promise.reject(error);
    }
);

// Response interceptor
api.interceptors.response.use(
    (response) => {
        // Log rate limit headers for demo tier awareness
        const rateLimitTier = response.headers["x-ratelimit-tier"];
        const rateLimitRemaining = response.headers["x-ratelimit-remaining"];
        const demoNotice = response.headers["x-demo-notice"];

        if (rateLimitTier === "demo" && rateLimitRemaining) {
            console.log(
                `Demo tier - Requests remaining: ${rateLimitRemaining}`
            );
            if (parseInt(rateLimitRemaining) < 10) {
                console.warn(
                    "Demo tier rate limit approaching. Consider upgrading for higher limits."
                );
            }
        }

        return response;
    },
    (error) => {
        // Handle common errors
        if (error.response?.status === 401) {
            // Handle unauthorized access
            console.error("Unauthorized access");
        } else if (error.response?.status === 429) {
            // Handle rate limiting
            const errorData = error.response.data;
            console.warn("Rate limit exceeded:", errorData);

            // Show user-friendly rate limit message
            if (errorData.tier === "demo") {
                const message =
                    errorData.message ||
                    "Demo tier rate limit exceeded. Please wait before making more requests.";
                console.error("Demo Rate Limit:", message);

                // You can dispatch to a global notification system here
                // For example: store.dispatch(showNotification({ type: 'warning', message }));

                // Add rate limit info to error for component handling
                error.rateLimitInfo = {
                    tier: errorData.tier,
                    retryAfter: errorData.retryAfter,
                    upgradeInfo: errorData.upgradeInfo,
                    message: errorData.message,
                };
            }
        } else if (error.response?.status >= 500) {
            // Handle server errors
            console.error("Server error:", error.response.data);
        }
        return Promise.reject(error);
    }
);

// Songs API functions
export const fetchSongs = async ({
    page = 1,
    limit = 10,
    search = "",
} = {}) => {
    const params = { page, limit };
    if (search && search.trim()) {
        params.search = search.trim();
    }

    const response = await api.get("/songs", {
        params,
    });
    return response;
};

export const fetchSongById = async (id) => {
    const response = await api.get(`/songs/${id}`);
    return response;
};

export const createSong = async (songData) => {
    const response = await api.post("/songs", songData);
    return response;
};

export const updateSong = async (id, songData) => {
    const response = await api.put(`/songs/${id}`, songData);
    return response;
};

export const deleteSong = async (id) => {
    const response = await api.delete(`/songs/${id}`);
    return response;
};

// Health check
export const healthCheck = async () => {
    const response = await api.get("/health");
    return response;
};

export default api;
