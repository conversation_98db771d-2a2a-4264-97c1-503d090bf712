{"name": "song-management-frontend", "version": "1.0.0", "description": "React frontend for song management application", "main": "src/index.js", "scripts": {"dev": "webpack serve --mode development", "build": "webpack --mode production", "start": "webpack serve --mode development", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["react", "redux", "emotion", "webpack", "songs"], "author": "", "license": "MIT", "dependencies": {"@emotion/css": "^11.11.2", "@emotion/react": "^11.11.1", "@emotion/styled": "^11.11.0", "@reduxjs/toolkit": "^2.0.1", "aos": "^2.3.4", "axios": "^1.6.2", "react": "^18.2.0", "react-dom": "^18.2.0", "react-redux": "^9.0.4", "redux-saga": "^1.2.3"}, "devDependencies": {"@babel/core": "^7.23.6", "@babel/preset-env": "^7.23.6", "@babel/preset-react": "^7.23.3", "babel-loader": "^9.1.3", "css-loader": "^6.8.1", "file-loader": "^6.2.0", "html-webpack-plugin": "^5.6.0", "style-loader": "^3.3.3", "url-loader": "^4.1.1", "webpack": "^5.89.0", "webpack-cli": "^5.1.4", "webpack-dev-server": "^4.15.1"}}