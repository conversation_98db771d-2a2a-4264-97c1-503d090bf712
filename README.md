# Song Management Application

A full-stack application for managing a list of songs with CRUD operations, pagination, and responsive design.

## Tech Stack

### Frontend
- **React** (Functional components with hooks)
- **Redux Toolkit** + **Redux-Saga** (State management and side effects)
- **Emotion/Styled System** (CSS-in-JS and theming)
- **Custom Webpack Configuration** (Manual setup without Create React App)

### Backend
- **Node.js** + **Express** (REST API server)
- **In-memory data storage** (Dummy song data)

## Features

- ✅ Paginated song list display
- ✅ CRUD operations (Create, Read, Update, Delete)
- ✅ Responsive design with Emotion styling
- ✅ Redux state management with Redux-Saga
- ✅ Custom Webpack configuration

## Project Structure

```
song-management-app/
├── frontend/           # React frontend application
├── backend/            # Node.js/Express backend API
├── package.json        # Root package.json with scripts
└── README.md          # This file
```

## Quick Start

1. **Install dependencies for both frontend and backend:**
   ```bash
   npm run install:all
   ```

2. **Start both frontend and backend in development mode:**
   ```bash
   npm run dev
   ```

3. **Access the application:**
   - Frontend: http://localhost:3000
   - Backend API: http://localhost:5000

## API Endpoints

All API endpoints are prefixed with `/api` and return JSON responses.

### Base URL
- **Development**: `http://localhost:5000/api`
- **Production**: `/api` (served from the same domain)

### Health Check

#### GET /api/health
Check if the API server is running.

**Response:**
```json
{
  "status": "OK",
  "message": "Song Management API is running",
  "timestamp": "2025-07-18T17:50:03.055Z"
}
```

### Songs API

| Method | Endpoint | Description | Parameters |
|--------|----------|-------------|------------|
| GET | `/api/songs` | Get paginated list of songs | `page`, `limit` |
| GET | `/api/songs/:id` | Get a specific song | - |
| POST | `/api/songs` | Create a new song | Song object in body |
| PUT | `/api/songs/:id` | Update an existing song | Song object in body |
| DELETE | `/api/songs/:id` | Delete a song | - |

### Request/Response Examples

#### GET /api/songs
**Query Parameters:**
- `page` (optional): Page number (default: 1)
- `limit` (optional): Items per page (default: 10)

**Response:**
```json
{
  "songs": [
    {
      "id": "1",
      "title": "Bohemian Rhapsody",
      "artist": "Queen",
      "album": "A Night at the Opera",
      "year": 1975,
      "genre": "Rock",
      "duration": "5:55"
    }
  ],
  "pagination": {
    "currentPage": 1,
    "totalPages": 5,
    "totalItems": 50,
    "itemsPerPage": 10
  }
}
```

#### POST /api/songs
**Request Body:**
```json
{
  "title": "New Song",
  "artist": "Artist Name",
  "album": "Album Name",
  "year": 2023,
  "genre": "Pop",
  "duration": "3:30"
}
```

**Response:**
```json
{
  "id": "51",
  "title": "New Song",
  "artist": "Artist Name",
  "album": "Album Name",
  "year": 2023,
  "genre": "Pop",
  "duration": "3:30"
}
```

#### PUT /api/songs/:id
**Request Body:** Same as POST

**Response:** Updated song object

#### DELETE /api/songs/:id
**Response:**
```json
{
  "message": "Song deleted successfully"
}
```

### Error Responses

All endpoints may return the following error responses:

#### 400 Bad Request
```json
{
  "error": "Validation Error",
  "message": "All fields are required: title, artist, album, year, genre, duration"
}
```

#### 404 Not Found
```json
{
  "error": "Not Found",
  "message": "Song not found"
}
```

#### 500 Internal Server Error
```json
{
  "error": "Server Error",
  "message": "Failed to retrieve songs"
}
```

### Data Validation

#### Song Object Schema
```json
{
  "title": "string (required)",
  "artist": "string (required)",
  "album": "string (required)",
  "year": "number (required, 1900-current year)",
  "genre": "string (required)",
  "duration": "string (required, format: MM:SS)"
}
```

#### Pagination Parameters
- `page`: Integer, minimum 1 (default: 1)
- `limit`: Integer, minimum 1 (default: 10)

### Rate Limiting (Demo Tier)

This application implements comprehensive rate limiting for demo/free tier usage:

#### Demo Tier Limits
- **100 requests per hour** - Total API calls allowed per hour
- **10 requests per minute** - Burst protection limit
- **Progressive slowdown** - Responses get slower after 5 requests/minute
- **Maximum delay** - Up to 2 seconds additional delay for excessive requests

#### Rate Limit Headers
The API returns the following headers to help clients manage their usage:
- `X-RateLimit-Tier: demo` - Indicates the current tier
- `X-RateLimit-Hourly-Limit: 100` - Hourly request limit
- `X-RateLimit-Minute-Limit: 10` - Per-minute request limit
- `X-Demo-Notice` - Information about demo tier limitations

#### Rate Limit Responses
When limits are exceeded, the API returns:
```json
{
  "error": "Demo Tier Rate Limit Exceeded",
  "message": "Demo tier allows 100 requests per hour. Please upgrade for higher limits.",
  "retryAfter": "1 hour",
  "tier": "demo",
  "upgradeInfo": "Contact support to upgrade your account for higher rate limits.",
  "timestamp": "2024-01-01T12:00:00.000Z"
}
```

#### Frontend Integration
The frontend automatically:
- Monitors rate limit headers
- Displays warnings when approaching limits
- Shows user-friendly error messages for rate limit violations
- Provides upgrade information when limits are exceeded

#### Testing Rate Limits
To test the rate limiting functionality:

1. **Quick burst test** - Make 15+ requests rapidly to trigger minute limit
2. **Sustained test** - Make 100+ requests over time to trigger hourly limit
3. **Health check** - The `/api/health` endpoint has separate, more lenient limits

Example using curl:
```bash
# Test burst limit (will trigger after 10 requests)
for i in {1..15}; do curl http://localhost:5000/api/songs; done

# Check health endpoint with rate limit info
curl http://localhost:5000/api/health
```

#### Configuration
Rate limiting is configured in `backend/middleware/rateLimiting.js`:
- Modify `DEMO_TIER_CONFIG` to adjust limits
- Uses IP-based identification for demo users
- In production, consider using Redis for distributed rate limiting

## Development

### Backend Development
```bash
cd backend
npm run dev
```

### Frontend Development
```bash
cd frontend
npm run dev
```

### Building for Production
```bash
npm run build
```

## Architecture

### Frontend Architecture
- **React 18** with functional components and hooks
- **Redux Toolkit** for state management
- **Redux-Saga** for handling side effects and API calls
- **Emotion** for CSS-in-JS styling with a comprehensive theme system
- **Custom Webpack** configuration for build optimization
- **Responsive design** that works on desktop and mobile devices

### Backend Architecture
- **Node.js** with **Express.js** framework
- **In-memory data storage** with dummy data (40 songs)
- **RESTful API** design with proper HTTP status codes
- **CORS** enabled for cross-origin requests
- **Request validation** and error handling
- **Structured logging** with Morgan

### Key Features Implemented
- ✅ **Paginated song listing** with 10 songs per page
- ✅ **CRUD operations** (Create, Read, Update, Delete)
- ✅ **Search functionality** with debounced input
- ✅ **Responsive design** using Emotion's styled components
- ✅ **Loading states** and error handling
- ✅ **Toast notifications** for user feedback
- ✅ **Modal dialogs** for forms and confirmations
- ✅ **Form validation** with real-time feedback
- ✅ **Custom Webpack configuration** (no Create React App)

## Development

### Prerequisites
- Node.js 16+ and npm
- Git

### Local Development Setup

1. **Clone and install:**
   ```bash
   git clone <repository-url>
   cd song-management-app
   npm run install:all
   ```

2. **Start development servers:**
   ```bash
   # Start both frontend and backend
   npm run dev

   # Or start individually:
   npm run backend:dev    # Backend on port 5000
   npm run frontend:dev   # Frontend on port 3000
   ```

3. **Access the application:**
   - Frontend: http://localhost:3000
   - Backend API: http://localhost:5000/api
   - Health check: http://localhost:5000/api/health

### Project Structure
```
song-management-app/
├── frontend/                 # React frontend
│   ├── src/
│   │   ├── components/      # Reusable UI components
│   │   ├── pages/          # Page components
│   │   ├── store/          # Redux store, slices, and sagas
│   │   ├── services/       # API service layer
│   │   ├── styles/         # Theme and global styles
│   │   └── utils/          # Utility functions
│   ├── public/             # Static assets
│   ├── webpack.config.js   # Custom Webpack configuration
│   └── package.json
├── backend/                 # Node.js backend
│   ├── routes/             # API route handlers
│   ├── data/               # Data models and dummy data
│   ├── server.js           # Express server setup
│   └── package.json
├── package.json            # Root package.json with scripts
└── README.md              # This file
```

### Available Scripts

#### Root Level
- `npm run dev` - Start both frontend and backend
- `npm run install:all` - Install dependencies for both projects
- `npm run build` - Build frontend for production
- `npm start` - Start backend in production mode

#### Backend (`cd backend`)
- `npm run dev` - Start with nodemon (auto-restart)
- `npm start` - Start in production mode

#### Frontend (`cd frontend`)
- `npm run dev` - Start development server with hot reload
- `npm run build` - Build for production

## Deployment

### Production Build
```bash
# Build the frontend
npm run build

# Start the backend
npm start
```

### Environment Variables
Create `.env` files for different environments:

**Backend (.env):**
```
NODE_ENV=production
PORT=5000
```

**Frontend (.env):**
```
NODE_ENV=production
REACT_APP_API_URL=/api
```

## License

MIT
